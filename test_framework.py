#!/usr/bin/env python3
"""
测试框架脚本
验证Auto-Cutter框架的基本功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from database.models import DatabaseManager
from utils.logger import get_logger

logger = get_logger(__name__)


def test_configuration():
    """测试配置系统"""
    print("=" * 50)
    print("测试配置系统")
    print("=" * 50)

    print(f"项目根目录: {settings.PROJECT_ROOT}")
    print(f"数据库路径: {settings.DATABASE_PATH}")
    print(f"片段目录: {settings.CLIPS_DIR}")
    print(f"输出目录: {settings.OUTPUT_DIR}")
    print(f"区块时长: {settings.CHUNK_DURATION_MINUTES} 分钟")
    print(f"最大并发请求: {settings.MAX_CONCURRENT_REQUESTS}")

    # 验证配置
    errors = settings.validate()
    if errors:
        print("\n配置错误:")
        for error in errors:
            print(f"  - {error}")
        return False
    else:
        print("\n✅ 配置验证通过")
        return True


def test_database():
    """测试数据库系统"""
    print("\n" + "=" * 50)
    print("测试数据库系统")
    print("=" * 50)

    try:
        # 创建数据库管理器
        db_manager = DatabaseManager(settings.DATABASE_PATH)
        print(f"✅ 数据库初始化成功: {settings.DATABASE_PATH}")

        # 测试创建视频记录
        video_id = db_manager.create_video_record("测试视频", 3600.0)
        print(f"✅ 创建视频记录成功，ID: {video_id}")

        # 测试获取视频记录
        video = db_manager.get_video_by_name("测试视频")
        if video:
            print(f"✅ 获取视频记录成功: {video['video_name']}")
        else:
            print("❌ 获取视频记录失败")
            return False

        # 测试状态管理
        db_manager.update_stage_status(video_id, 1, "processing", progress_info="测试进度")
        status = db_manager.get_stage_status(video_id, 1)
        if status and status["status"] == "processing":
            print("✅ 状态管理测试成功")
        else:
            print("❌ 状态管理测试失败")
            return False

        # 测试获取所有视频
        videos = db_manager.get_all_videos()
        print(f"✅ 数据库中共有 {len(videos)} 个视频记录")

        return True

    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


def test_ai_utils():
    """测试AI工具"""
    print("\n" + "=" * 50)
    print("测试AI工具")
    print("=" * 50)

    try:
        from utils.ai_utils import ai_client

        # 检查API密钥
        if not settings.VOLCENGINE_API_KEY or settings.VOLCENGINE_API_KEY == "your_openai_api_key_here":
            print("⚠️  未配置OpenAI API密钥，跳过AI测试")
            return True

        print("🔄 测试AI调用...")

        # 简单的AI调用测试
        try:
            result = ai_client.call_ai("请回答：1+1等于几？", max_tokens=50)
            if result:
                print(f"✅ AI调用成功，返回: {result[:100]}...")
                return True
            else:
                print("❌ AI调用返回空结果")
                return False
        except Exception as e:
            print(f"❌ AI调用失败: {e}")
            return False

    except Exception as e:
        print(f"❌ AI工具测试失败: {e}")
        return False


def test_stages():
    """测试处理阶段"""
    print("\n" + "=" * 50)
    print("测试处理阶段")
    print("=" * 50)

    try:
        from stages.stage1_analysis import Stage1Analysis
        from stages.stage2_research import Stage2Research
        from stages.stage3_outline import Stage3Outline
        from stages.stage4_production import Stage4Production

        # 创建数据库和视频记录
        db_manager = DatabaseManager(settings.DATABASE_PATH)
        video_id = db_manager.create_video_record("阶段测试视频", 1800.0)

        # 测试各个阶段的初始化
        stages = [(1, Stage1Analysis), (2, Stage2Research), (3, Stage3Outline), (4, Stage4Production)]

        for stage_num, stage_class in stages:
            try:
                stage = stage_class(db_manager, video_id)
                print(f"✅ 阶段{stage_num}（{stage.stage_name}）初始化成功")

                # 测试前置条件检查
                can_run, error_msg = stage.can_run()
                if stage_num == 1:
                    # 阶段1应该可以运行（如果有片段文件）
                    print(f"   阶段{stage_num}运行检查: {'可以运行' if can_run else f'不能运行 - {error_msg}'}")
                else:
                    # 其他阶段需要前置条件
                    print(f"   阶段{stage_num}运行检查: {'可以运行' if can_run else f'需要前置条件 - {error_msg}'}")

            except Exception as e:
                print(f"❌ 阶段{stage_num}初始化失败: {e}")
                return False

        return True

    except Exception as e:
        print(f"❌ 阶段测试失败: {e}")
        return False


def test_clips_directory():
    """测试片段目录"""
    print("\n" + "=" * 50)
    print("测试片段目录")
    print("=" * 50)

    clips_dir = settings.CLIPS_DIR
    print(f"片段目录: {clips_dir}")

    if not clips_dir.exists():
        print("⚠️  片段目录不存在")
        return False

    # 统计片段文件
    clip_files = list(clips_dir.glob("*.mp4"))
    print(f"找到 {len(clip_files)} 个MP4片段文件")

    if clip_files:
        # 显示前几个文件
        print("片段文件示例:")
        for i, clip_file in enumerate(clip_files[:5]):
            size_mb = clip_file.stat().st_size / (1024 * 1024)
            print(f"  {i + 1}. {clip_file.name} ({size_mb:.1f} MB)")

        if len(clip_files) > 5:
            print(f"  ... 还有 {len(clip_files) - 5} 个文件")

    return len(clip_files) > 0


def test_main_program():
    """测试主程序"""
    print("\n" + "=" * 50)
    print("测试主程序")
    print("=" * 50)

    try:
        from main import AutoCutter

        # 创建主控制器
        auto_cutter = AutoCutter()
        print("✅ 主控制器创建成功")

        # 测试列出视频
        print("🔄 测试列出视频...")
        auto_cutter.list_videos()

        return True

    except Exception as e:
        print(f"❌ 主程序测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试Auto-Cutter框架")
    print("=" * 60)

    tests = [
        ("配置系统", test_configuration),
        ("数据库系统", test_database),
        ("AI工具", test_ai_utils),
        ("处理阶段", test_stages),
        ("片段目录", test_clips_directory),
        ("主程序", test_main_program),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results.append((test_name, False))

    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1

    print("-" * 60)
    print(f"总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("\n🎉 所有测试通过！框架运行正常。")
        print("\n下一步:")
        print("1. 配置 .env 文件中的 VOLCENGINE_API_KEY")
        print("2. 运行 'python main.py --create \"我的视频\" 3600' 创建视频记录")
        print("3. 运行 'python main.py --video 1 --stage 1' 开始处理")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查配置和依赖。")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
