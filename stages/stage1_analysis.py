"""
阶段1：微观场景分析与数据构建
包含场景检测、分割、AI分析和向量化
"""

import concurrent.futures
import json
import os
import queue
import threading
from pathlib import Path
from typing import Any, Dict, List, Optional

import numpy as np
from deepface import DeepFace

from config.settings import settings
from utils.shared_state import shutdown_event
from stages.base import BaseStage
from utils.ai_utils import ai_client
from utils.storage_utils import tos_client
from utils.vector_utils import video_vectorizer
from utils.video_utils import video_processor


class Stage1Analysis(BaseStage):
    """阶段1：微观场景分析与数据构建"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self._db_queue = queue.Queue()
        self._stop_db_writer = threading.Event()

    def _db_writer_worker(self):
        """从队列中获取并执行数据库写入任务"""
        self.logger.info("数据库写入线程已启动。")
        while not self._stop_db_writer.is_set() or not self._db_queue.empty():
            try:
                # 设置超时以允许线程在队列为空时检查停止信号
                db_task, args = self._db_queue.get(timeout=1)
                try:
                    db_task(*args)
                except Exception as e:
                    self.logger.error(f"执行数据库任务时出错: {e}")
                finally:
                    # 确保即使任务执行失败，task_done()也总能被调用
                    self._db_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                # 这个异常捕获处理 get() 本身可能出现的其他问题
                self.logger.error(f"数据库写入线程的队列操作出错: {e}")
        self.logger.info("数据库写入线程已停止。")

    @property
    def stage_number(self) -> int:
        return 1

    @property
    def stage_name(self) -> str:
        return "微观场景分析"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        # 阶段1没有严格的前置阶段，文件存在性在执行时检查
        return True, ""

    def execute(self, force_level: Optional[str] = None) -> bool:
        """执行微观场景分析"""
        try:
            # --- 步骤 1: 并行分析场景内容 ---
            # self.update_progress("开始并行分析场景内容")
            # if not self._perform_scene_analysis(force_level=force_level):
            #     return False

            if shutdown_event.is_set():
                return False

            # --- 步骤 2: 串行提取人脸特征 ---
            if not self._perform_face_extraction_sequentially():
                return False

            return True

        except Exception as e:
            self.logger.error(f"执行阶段1时出错: {e}", exc_info=True)
            return False

    def _process_single_scene(self, scene_index: int, start_time: float, end_time: float, clip_path: Path) -> bool:
        """处理单个已提取场景的流程：生成低质版本、上传低质版、分析、向量化、保存"""
        try:
            # --- 1. 创建低质量视频和音频文件 ---
            low_quality_video_path = clip_path.with_suffix(".lowres.mp4")
            audio_path = clip_path.with_suffix(".mp3")
            if not video_processor.create_analysis_assets(
                clip_path, low_quality_video_path, audio_path, settings.LOW_QUALITY_BITRATE, settings.AUDIO_BITRATE
            ):
                raise RuntimeError("创建低质量视频或提取音频失败")

            # --- 2. 上传低质量版本到TOS ---
            low_quality_object_key = f"clips/low-res/{low_quality_video_path.name}"
            low_quality_clip_url = tos_client.upload_file(low_quality_video_path, low_quality_object_key)
            if not low_quality_clip_url:
                raise RuntimeError(f"上传低画质片段 {low_quality_video_path.name} 失败")

            # --- 3. 执行AI分析 (目前仅视觉) ---
            visual_analysis_result = ai_client.analyze_scene(low_quality_clip_url, end_time - start_time)
            if "error" in visual_analysis_result:
                raise RuntimeError(f"AI视觉分析失败: {visual_analysis_result.get('raw_content', '未知错误')}")

            # 音频分析暂时禁用，返回空结果，为未来功能预留接口
            audio_analysis_result = {}
            self.logger.debug("音频分析已禁用，跳过AI调用。")

            # --- 4. 向量化高画质版本 ---
            embedding_vector = video_vectorizer.vectorize_clip(clip_path)
            clip_file_size = clip_path.stat().st_size

            # --- 5. 保存分析结果（不含人脸） ---
            self._save_scene_analysis(
                start_time,
                end_time,
                str(clip_path.resolve()),
                clip_file_size,
                low_quality_clip_url,
                str(audio_path.resolve()),
                visual_analysis_result,
                audio_analysis_result,
                embedding_vector,
            )
            return True
        except Exception as e:
            self.logger.error(f"处理场景 {start_time:.2f}s-{end_time:.2f}s 时出错: {e}")
            self._save_scene_error(start_time, end_time, str(e))
            return False

    def _get_scene_status(self, start_time: float, end_time: float) -> Optional[str]:
        """获取单个场景的状态 (completed, failed, or None if not exists)"""
        from database.models import Scenes

        with self.db_manager.get_session() as session:
            scene = (
                session.query(Scenes.status)
                .filter_by(video_id=self.video_id, start_time=start_time, end_time=end_time)
                .scalar()
            )
            return scene

    def _delete_scene_record(self, start_time: float, end_time: float):
        """删除指定的场景记录，以便重试"""
        from database.models import Scenes

        with self.db_manager.get_session() as session:
            session.query(Scenes).filter_by(video_id=self.video_id, start_time=start_time, end_time=end_time).delete()
            session.commit()

    def _scene_exists(self, start_time: float, end_time: float) -> bool:
        """检查具有相同时间戳的场景是否已存在于数据库中 (ORM 版本)"""
        from database.models import Scenes

        with self.db_manager.get_session() as session:
            return (
                session.query(Scenes)
                .filter_by(video_id=self.video_id, start_time=start_time, end_time=end_time)
                .first()
                is not None
            )

    def _save_scene_analysis(
        self,
        start_time: float,
        end_time: float,
        clip_local_path: str,
        clip_file_size: int,
        low_quality_clip_url: str,
        audio_local_path: str,
        visual_analysis: Dict[str, Any],
        audio_analysis: Dict[str, Any],
        embedding_vector: Optional[np.ndarray],
    ):
        """将场景分析结果放入数据库写入队列"""
        args = (
            start_time,
            end_time,
            clip_local_path,
            clip_file_size,
            low_quality_clip_url,
            audio_local_path,
            visual_analysis,
            audio_analysis,
            embedding_vector,
        )
        self._db_queue.put((self._execute_save_scene_analysis, args))

    def _execute_save_scene_analysis(
        self,
        start_time: float,
        end_time: float,
        clip_local_path: str,
        clip_file_size: int,
        low_quality_clip_url: str,
        audio_local_path: str,
        visual_analysis: Dict[str, Any],
        audio_analysis: Dict[str, Any],
        embedding_vector: Optional[np.ndarray],
    ):
        """实际执行保存场景分析结果的数据库操作 (ORM 版本)"""
        from database.models import Scenes  # 只导入 Scenes

        shot_analysis = visual_analysis.get("shot_analysis") or {}
        dialogue = visual_analysis.get("dialogue") or audio_analysis.get("transcript", "")

        new_scene = Scenes(
            video_id=self.video_id,
            start_time=start_time,
            end_time=end_time,
            clip_url=clip_local_path,
            low_quality_clip_url=low_quality_clip_url,
            audio_file_path=audio_local_path,
            audio_analysis=json.dumps(audio_analysis, ensure_ascii=False),
            clip_file_size=clip_file_size,
            people=visual_analysis.get("people", ""),
            setting=visual_analysis.get("setting", ""),
            action=visual_analysis.get("main_action", ""),
            dialogue=dialogue,
            sound_events=visual_analysis.get("sound_events", ""),
            shot_type=shot_analysis.get("shot_type", ""),
            camera_angle=shot_analysis.get("camera_angle", ""),
            camera_movement=shot_analysis.get("camera_movement", ""),
            composition=shot_analysis.get("composition", ""),
            lighting=shot_analysis.get("lighting", ""),
            embedding_vector=embedding_vector.tobytes() if embedding_vector is not None else None,
            status="completed",
        )

        with self.db_manager.get_session() as session:
            session.add(new_scene)

    def _save_scene_error(self, start_time: float, end_time: float, error_message: str, clip_url: Optional[str] = None):
        """将场景分析错误放入数据库写入队列"""
        self._db_queue.put((self._execute_save_scene_error, (start_time, end_time, error_message, clip_url)))

    def _execute_save_scene_error(
        self, start_time: float, end_time: float, error_message: str, clip_url: Optional[str] = None
    ):
        """实际执行保存场景分析错误的数据库操作 (ORM 版本)"""
        from database.models import Scenes

        new_scene_error = Scenes(
            video_id=self.video_id,
            start_time=start_time,
            end_time=end_time,
            clip_url=clip_url,
            status="failed",
            error_message=error_message,
        )
        with self.db_manager.get_session() as session:
            session.add(new_scene_error)

    def _extract_face_embeddings(self, clip_path: Path) -> List[Dict[str, Any]]:
        """从视频片段中提取所有人脸的嵌入向量"""
        try:
            # --- 方案一：强制使用CPU，禁用GPU加速 ---
            # 在调用deepface前，设置环境变量禁用CUDA/GPU设备
            os.environ["CUDA_VISIBLE_DEVICES"] = "-1"

            # DeepFace.represent 在一个视频中找到多个人脸并返回它们的嵌入
            embedding_objs = DeepFace.represent(
                img_path=str(clip_path),
                model_name="VGG-Face",
                detector_backend="retinaface",
                enforce_detection=False,  # 如果没找到人脸，不要抛出异常
                align=True,
            )

            results = []
            for obj in embedding_objs:
                if obj["face_confidence"] > 0.9:  # 只接受高置信度的人脸
                    results.append(
                        {
                            "embedding": np.array(obj["embedding"], dtype=np.float32),
                            "bounding_box": obj["facial_area"],
                        }
                    )

            self.logger.debug(f"在 {clip_path.name} 中提取到 {len(results)} 个高质量人脸。")
            return results
        except Exception as e:
            # 如果deepface库出错 (例如，视频文件损坏)
            self.logger.warning(f"在 {clip_path.name} 中提取人脸时失败: {e}")
            return []

    def _perform_face_extraction_sequentially(self) -> bool:
        """串行执行人脸提取，并更新数据库记录。"""
        self.update_progress("开始串行提取人脸特征")
        from database.models import Scenes, FaceEmbeddings

        with self.db_manager.get_session() as session:
            # 只查询我们需要的数据（ID和URL），而不是整个ORM对象
            subquery = session.query(FaceEmbeddings.scene_id).distinct()
            scenes_to_process = (
                session.query(Scenes.id, Scenes.clip_url)  # 修改这里
                .filter(
                    Scenes.video_id == self.video_id,
                    Scenes.status == "completed",
                    ~Scenes.id.in_(subquery),
                )
                .order_by(Scenes.start_time)
                .all()
            )

        if not scenes_to_process:
            self.logger.info("没有需要提取人脸的新场景。")
            return True

        total_scenes = len(scenes_to_process)
        self.logger.info(f"将为 {total_scenes} 个场景串行提取人脸特征...")

        # 循环遍历元组 (scene_id, clip_url)
        for i, (scene_id, clip_url) in enumerate(scenes_to_process):
            if shutdown_event.is_set():
                self.logger.warning("检测到关闭信号，中断人脸提取。")
                return False

            self.update_progress(f"提取人脸: {i + 1}/{total_scenes}")
            
            # clip_url 现在是一个字符串，而不是ORM属性
            clip_path = Path(clip_url)
            if not clip_path.exists():
                self.logger.warning(f"找不到片段文件 {clip_path}，跳过场景 {scene_id}。")
                continue

            face_embeddings = self._extract_face_embeddings(clip_path)

            # 更新数据库
            if face_embeddings:
                with self.db_manager.get_session() as update_session:
                    for face_data in face_embeddings:
                        new_face = FaceEmbeddings(
                            scene_id=scene_id,  # 使用 scene_id
                            embedding=face_data["embedding"].tobytes(),
                            bounding_box=face_data["bounding_box"],
                        )
                        update_session.add(new_face)

        self.logger.info("所有人脸特征提取完成。")
        return True

    def _extract_single_clip(
        self, scene_info: Dict[str, Any], input_video_path: Path, clips_dir: Path, file_hash: str
    ) -> Optional[Dict[str, Any]]:
        """辅助函数：提取单个视频片段并返回其信息，会重试失败的场景。"""
        i, start_time, end_time = scene_info["index"], scene_info["start"], scene_info["end"]

        # 检查场景状态
        scene_status = self._get_scene_status(start_time, end_time)
        if scene_status == "completed":
            self.logger.debug(f"场景 {start_time:.2f}s-{end_time:.2f}s 已成功分析，跳过。")
            return None
        elif scene_status == "failed":
            self.logger.warning(f"场景 {start_time:.2f}s-{end_time:.2f}s 上次分析失败，将重新尝试。")
            # 删除旧的失败记录，以便重新插入
            self._delete_scene_record(start_time, end_time)

        # 提取片段逻辑保持不变
        clip_filename = f"{file_hash}-Scene-{i + 1:04d}-{start_time:.2f}s.mp4"
        clip_path = clips_dir / clip_filename

        if not clip_path.exists():
            if not video_processor.extract_clip(input_video_path, start_time, end_time, clip_path):
                self.logger.error(f"提取片段 {clip_path.name} 失败，跳过此场景。")
                self._save_scene_error(start_time, end_time, "提取片段失败")
                return None

        return {"index": i, "start": start_time, "end": end_time, "path": clip_path}

    def _perform_scene_analysis(self, force_level: Optional[str] = None) -> bool:
        """执行场景检测、分割和分析（先批量提取，后并发分析）"""
        video_info = self.get_video_info()
        if not video_info:
            self.logger.error("无法获取视频信息")
            return False

        # --- 1. 获取场景列表 (从缓存或重新检测) ---
        scene_list = self.db_manager.get_detected_scenes(self.video_id)
        if not scene_list or force_level == "full":
            if force_level == "full":
                self.logger.info("强制模式 'full': 正在重新进行场景检测...")
            input_path_str = video_info.get("input_file_path")
            if not input_path_str or not Path(input_path_str).exists():
                self.logger.error(f"视频文件路径无效或不存在: {input_path_str}")
                return False
            input_video_path = Path(input_path_str)
            scene_list = video_processor.detect_scenes(
                video_path=input_video_path, threshold=settings.SCENE_DETECTION_THRESHOLD
            )
            if not scene_list:
                self.logger.error("场景检测未能返回任何场景。")
                return False
            self.db_manager.save_detected_scenes(
                self.video_id, scene_list, f"threshold:{settings.SCENE_DETECTION_THRESHOLD}"
            )
        else:
            self.logger.info("使用缓存的场景检测结果。")

        # --- 2. 清理旧数据（如果强制执行） ---
        if force_level == "full":
            self.logger.info(f"强制模式 '{force_level}': 正在清理旧的场景分析数据...")
            # 清理旧的场景分析数据
            self.db_manager.clear_scene_analysis_data(self.video_id)
            # 注意：区块数据将在 _perform_chunk_analysis 中根据 force_level 清理

        # --- 3. 并行提取所有需要的视频片段 ---
        file_hash = video_info["file_hash"]
        input_video_path = Path(video_info["input_file_path"])
        clips_dir = settings.CLIPS_DIR

        scenes_to_process = []
        self.logger.info("开始并行提取视频片段...")

        # 建议的并行数：CPU核心数的一半，避免I/O和CPU过度竞争。可以调整。
        max_extract_workers = max(1, (os.cpu_count() or 1) // 2)
        self.logger.info(f"使用 {max_extract_workers} 个工作线程进行片段提取。")

        # 准备任务列表
        tasks = [{"index": i, "start": start, "end": end} for i, (start, end) in enumerate(scene_list)]

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_extract_workers) as executor:
            # 提交所有提取任务
            future_to_scene = {
                executor.submit(self._extract_single_clip, task, input_video_path, clips_dir, file_hash): task
                for task in tasks
            }

            total_tasks = len(future_to_scene)
            completed_tasks = 0
            for future in concurrent.futures.as_completed(future_to_scene):
                if shutdown_event.is_set():
                    self.logger.warning("检测到关闭信号，提取任务将被中断。")
                    # 取消所有未完成的任务
                    for f in future_to_scene:
                        f.cancel()
                    break

                try:
                    result = future.result()
                    if result:
                        scenes_to_process.append(result)
                except concurrent.futures.CancelledError:
                    pass
                except Exception as exc:
                    task_info = future_to_scene[future]
                    self.logger.error(f"提取场景 {task_info['index']} 时发生意外错误: {exc}")

                completed_tasks += 1
                self.update_progress(f"提取进度: {completed_tasks}/{total_tasks}")

        if shutdown_event.is_set():
            self.logger.info("由于用户中断，分析阶段未完成。")
            return False

        # 对结果按场景顺序排序，确保后续处理顺序正确
        scenes_to_process.sort(key=lambda x: x["index"])

        self.logger.info(f"片段提取完成，共准备好 {len(scenes_to_process)} 个新场景进行分析。")

        if not scenes_to_process:
            self.logger.info("所有场景均已分析完毕，无需处理。")
            return True

        # --- 4. 并发分析所有已提取的片段 ---
        completed_count = 0
        failed_count = 0

        db_writer_thread = threading.Thread(target=self._db_writer_worker, daemon=True)
        db_writer_thread.start()

        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_CONCURRENT_REQUESTS) as executor:
                future_to_scene = {
                    executor.submit(self._process_single_scene, s["index"], s["start"], s["end"], s["path"]): s
                    for s in scenes_to_process
                }

                submitted_count = len(future_to_scene)
                self.logger.info(f"已提交 {submitted_count} 个分析任务，等待处理完成...")

                cancelling = False
                for future in concurrent.futures.as_completed(future_to_scene):
                    if shutdown_event.is_set() and not cancelling:
                        self.logger.warning("检测到关闭信号，正在取消所有排队中的任务...")
                        cancelling = True
                        for f in future_to_scene:
                            f.cancel()

                    scene_info = future_to_scene[future]
                    try:
                        success = future.result()
                        if success:
                            completed_count += 1
                        else:
                            failed_count += 1
                    except concurrent.futures.CancelledError:
                        self.logger.debug(f"任务 {scene_info['path'].name} 已被取消。")
                        failed_count += 1
                    except Exception as exc:
                        self.logger.error(
                            f"处理场景 {scene_info['start']:.2f}s-{scene_info['end']:.2f}s 时产生异常: {exc}"
                        )
                        failed_count += 1

                    progress_info = f"分析中 - 已完成 {completed_count + failed_count}/{submitted_count} (成功: {completed_count}, 失败: {failed_count})"
                    self.update_progress(progress_info)

        finally:
            self.logger.info("分析循环结束，等待数据库写入完成...")
            self._db_queue.join()
            self._stop_db_writer.set()
            db_writer_thread.join()

        self.logger.info(
            f"场景分析完成。总提交任务数: {submitted_count}，成功: {completed_count}，失败: {failed_count}"
        )
        return failed_count == 0 and not shutdown_event.is_set()
