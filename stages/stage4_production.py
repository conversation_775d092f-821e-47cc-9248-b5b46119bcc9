"""
阶段4：自动化生产与合成
将抽象的大纲转化为具体的、可观看的视频成品
"""

from typing import Dict, Any, List
from pathlib import Path
import json

from stages.base import BaseStage
from utils.ai_utils import ai_client
from config.settings import settings


class Stage4Production(BaseStage):
    """阶段4：自动化生产与合成"""
    
    @property
    def stage_number(self) -> int:
        return 4
    
    @property
    def stage_name(self) -> str:
        return "自动化生产与合成"
    
    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        # 检查阶段3是否完成
        stage3_status = self.db_manager.get_stage_status(self.video_id, 3)
        if not stage3_status or stage3_status['status'] != 'completed':
            return False, "阶段3（融合知识生成全局大纲）尚未完成"
        
        # 检查是否有大纲数据
        with self.db_manager.get_connection() as conn:
            cursor = conn.execute(
                """SELECT COUNT(*) FROM outlines 
                   WHERE video_id = ? AND status = 'completed'""",
                (self.video_id,)
            )
            outline_count = cursor.fetchone()[0]
            
            if outline_count == 0:
                return False, "没有完成的大纲数据"
        
        return True, ""
    
    def execute(self) -> bool:
        """执行自动化生产与合成"""
        try:
            # 1. 获取大纲数据
            self.update_progress("获取大纲数据")
            outline = self._get_outline()
            if not outline:
                return False
            
            # 2. 为每个章节检索片段
            self.update_progress("开始片段检索")
            if not self._retrieve_clips_for_chapters(outline):
                return False
            
            # 3. 生成文案和配音
            self.update_progress("开始文案创作和配音")
            if not self._generate_scripts_and_audio(outline):
                return False
            
            # 4. 最终视频合成
            self.update_progress("开始视频合成")
            if not self._compose_final_video(outline):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"执行阶段4时出错: {e}")
            return False
    
    def _get_outline(self) -> Dict[str, Any]:
        """获取大纲数据"""
        with self.db_manager.get_connection() as conn:
            # 获取最新的大纲
            cursor = conn.execute(
                """SELECT * FROM outlines 
                   WHERE video_id = ? AND status = 'completed'
                   ORDER BY created_at DESC LIMIT 1""",
                (self.video_id,)
            )
            outline_row = cursor.fetchone()
            
            if not outline_row:
                self.logger.error("没有找到完成的大纲")
                return None
            
            outline = dict(outline_row)
            
            # 获取章节
            cursor = conn.execute(
                """SELECT * FROM chapters 
                   WHERE outline_id = ?
                   ORDER BY chapter_number""",
                (outline['id'],)
            )
            
            chapters = [dict(row) for row in cursor.fetchall()]
            outline['chapters'] = chapters
            
            self.logger.info(f"获取大纲成功，主题: {outline['theme']}, 章节数: {len(chapters)}")
            return outline
    
    def _retrieve_clips_for_chapters(self, outline: Dict[str, Any]) -> bool:
        """为每个章节检索相关片段"""
        chapters = outline['chapters']
        
        for i, chapter in enumerate(chapters):
            self.update_progress(f"检索章节 {i+1}/{len(chapters)} 的片段")
            
            try:
                # 将章节摘要转换为搜索条件
                search_conditions = self._convert_summary_to_search(chapter['summary'])
                
                # 根据搜索条件查询场景
                matching_scenes = self._query_scenes(search_conditions)
                
                # 保存检索结果（这里可以扩展数据库结构来存储）
                self.logger.info(f"章节 {chapter['chapter_number']} 找到 {len(matching_scenes)} 个匹配场景")
                
                # 将结果存储在章节的扩展信息中
                chapter['matching_scenes'] = matching_scenes
                
            except Exception as e:
                self.logger.error(f"检索章节 {chapter['chapter_number']} 片段时出错: {e}")
                chapter['matching_scenes'] = []
        
        return True
    
    def _convert_summary_to_search(self, summary: str) -> Dict[str, List[str]]:
        """将章节摘要转换为搜索条件"""
        try:
            from config.prompts import SUMMARY_TO_QUERY_PROMPT
            
            prompt = SUMMARY_TO_QUERY_PROMPT.format(chapter_summary=summary)
            result = ai_client.call_ai_json(prompt)
            
            if 'error' in result:
                self.logger.warning(f"转换搜索条件失败: {result['error']}")
                return self._default_search_conditions()
            
            return result
            
        except Exception as e:
            self.logger.error(f"转换搜索条件时出错: {e}")
            return self._default_search_conditions()
    
    def _default_search_conditions(self) -> Dict[str, List[str]]:
        """默认搜索条件"""
        return {
            "required_visuals": [],
            "required_emotions": [],
            "required_actions": [],
            "dialogue_keywords": []
        }
    
    def _query_scenes(self, search_conditions: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """根据搜索条件查询场景"""
        with self.db_manager.get_connection() as conn:
            # 构建动态SQL查询
            where_conditions = ["video_id = ?", "status = 'completed'"]
            params = [self.video_id]
            
            # 添加视觉描述条件
            for visual in search_conditions.get('required_visuals', []):
                if visual:
                    where_conditions.append("visual_description LIKE ?")
                    params.append(f"%{visual}%")
            
            # 添加情绪条件
            for emotion in search_conditions.get('required_emotions', []):
                if emotion:
                    where_conditions.append("emotion LIKE ?")
                    params.append(f"%{emotion}%")
            
            # 添加动作条件
            for action in search_conditions.get('required_actions', []):
                if action:
                    where_conditions.append("action LIKE ?")
                    params.append(f"%{action}%")
            
            # 添加对话关键词条件
            for keyword in search_conditions.get('dialogue_keywords', []):
                if keyword:
                    where_conditions.append("dialogue LIKE ?")
                    params.append(f"%{keyword}%")
            
            # 如果没有具体条件，返回所有场景
            if len(where_conditions) == 2:  # 只有基础条件
                where_conditions = ["video_id = ?", "status = 'completed'"]
                params = [self.video_id]
            
            query = f"""
                SELECT * FROM scenes 
                WHERE {' AND '.join(where_conditions)}
                ORDER BY start_time
                LIMIT 20
            """
            
            cursor = conn.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def _generate_scripts_and_audio(self, outline: Dict[str, Any]) -> bool:
        """生成文案和配音"""
        chapters = outline['chapters']
        
        for i, chapter in enumerate(chapters):
            self.update_progress(f"生成章节 {i+1}/{len(chapters)} 的文案和配音")
            
            try:
                # 生成文案
                script = self._generate_script(chapter)
                
                # 生成配音（目前只是模拟）
                audio_file = self._generate_audio(script, chapter['id'])
                
                # 更新章节记录
                with self.db_manager.get_connection() as conn:
                    conn.execute(
                        """UPDATE chapters 
                           SET script_content = ?, audio_file_path = ?, status = 'completed'
                           WHERE id = ?""",
                        (script, audio_file, chapter['id'])
                    )
                    conn.commit()
                
                self.logger.info(f"章节 {chapter['chapter_number']} 文案和配音生成完成")
                
            except Exception as e:
                self.logger.error(f"生成章节 {chapter['chapter_number']} 文案和配音时出错: {e}")
        
        return True
    
    def _generate_script(self, chapter: Dict[str, Any]) -> str:
        """生成章节文案"""
        try:
            from config.prompts import SCRIPT_WRITING_PROMPT
            
            # 准备场景描述
            scenes = chapter.get('matching_scenes', [])
            scene_descriptions = []
            
            for scene in scenes[:5]:  # 限制场景数量
                desc = f"镜头 {len(scene_descriptions)+1}: {scene.get('visual_description', '')} - {scene.get('action', '')}"
                scene_descriptions.append(desc)
            
            scenes_text = "\n".join(scene_descriptions) if scene_descriptions else "暂无具体镜头描述"
            
            # 估算时长（假设每个场景5秒）
            duration = len(scenes) * 5 if scenes else 30
            
            prompt = SCRIPT_WRITING_PROMPT.format(
                title=chapter['title'],
                summary=chapter['summary'],
                scene_descriptions=scenes_text,
                duration=duration
            )
            
            script = ai_client.call_ai(prompt)
            return script
            
        except Exception as e:
            self.logger.error(f"生成文案失败: {e}")
            return f"章节 {chapter['chapter_number']}: {chapter['title']} 的默认文案"
    
    def _generate_audio(self, script: str, chapter_id: int) -> str:
        """生成配音文件"""
        # 这里应该调用TTS服务生成音频文件
        # 目前只是返回模拟的文件路径
        audio_file = f"audio/chapter_{chapter_id}.mp3"
        
        self.logger.warning("TTS功能尚未实现，返回模拟音频文件路径")
        return audio_file
    
    def _compose_final_video(self, outline: Dict[str, Any]) -> bool:
        """合成最终视频"""
        try:
            self.update_progress("准备视频合成")
            
            # 收集所有章节的素材
            video_segments = []
            audio_segments = []
            
            for chapter in outline['chapters']:
                # 收集视频片段
                scenes = chapter.get('matching_scenes', [])
                for scene in scenes[:3]:  # 每章节最多3个场景
                    clip_file = self._get_clip_file(scene)
                    if clip_file:
                        video_segments.append(clip_file)
                
                # 收集音频文件
                audio_file = chapter.get('audio_file_path')
                if audio_file:
                    audio_segments.append(audio_file)
            
            # 生成最终视频
            output_file = settings.OUTPUT_DIR / f"final_cut_{self.video_id}.mp4"
            
            # 这里应该使用MoviePy进行实际的视频合成
            # 目前只是创建一个标记文件
            self._create_composition_info(output_file, video_segments, audio_segments, outline)
            
            self.logger.info(f"视频合成完成，输出文件: {output_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"视频合成失败: {e}")
            return False
    
    def _get_clip_file(self, scene: Dict[str, Any]) -> str:
        """获取场景对应的视频片段文件"""
        # 根据场景的时间信息查找对应的片段文件
        start_time = scene['start_time']
        
        # 简单的文件名匹配逻辑
        clips_dir = settings.CLIPS_DIR
        for clip_file in clips_dir.glob("*.mp4"):
            # 这里需要根据实际的文件命名规则来匹配
            if f"Scene-{int(start_time//5)+1:04d}" in clip_file.name:
                return str(clip_file)
        
        return None
    
    def _create_composition_info(self, output_file: Path, video_segments: List[str], 
                               audio_segments: List[str], outline: Dict[str, Any]):
        """创建合成信息文件（替代实际的视频合成）"""
        composition_info = {
            "output_file": str(output_file),
            "theme": outline['theme'],
            "video_segments": video_segments,
            "audio_segments": audio_segments,
            "chapters": [
                {
                    "number": ch['chapter_number'],
                    "title": ch['title'],
                    "summary": ch['summary'],
                    "script": ch.get('script_content', ''),
                    "audio": ch.get('audio_file_path', '')
                }
                for ch in outline['chapters']
            ]
        }
        
        info_file = output_file.with_suffix('.json')
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(composition_info, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"合成信息已保存到: {info_file}")
        
        # 创建一个空的视频文件作为占位符
        output_file.touch()
        self.logger.info(f"占位符视频文件已创建: {output_file}")
