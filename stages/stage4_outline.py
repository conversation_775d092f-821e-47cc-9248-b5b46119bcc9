"""
阶段4：融合知识生成全局大纲
将视频的内部理解与外部知识深度融合，创造有独特视角和深度的最终叙事大纲
"""

from typing import Dict, Any, List

from stages.base import BaseStage
from utils.ai_utils import ai_client


class Stage4Outline(BaseStage):
    """阶段4：融合知识生成全局大纲"""
    
    @property
    def stage_number(self) -> int:
        return 4
    
    @property
    def stage_name(self) -> str:
        return "融合知识生成全局大纲"
    
    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        # 检查阶段3是否完成
        stage3_status = self.db_manager.get_stage_status(self.video_id, 3)
        if not stage3_status or stage3_status['status'] != 'completed':
            return False, "阶段3（知识整合与提炼）尚未完成"
        
        # 检查是否有区块数据
        with self.db_manager.get_connection() as conn:
            cursor = conn.execute(
                "SELECT COUNT(*) FROM chunks WHERE video_id = ? AND status = 'completed'",
                (self.video_id,)
            )
            chunk_count = cursor.fetchone()[0]
            
            if chunk_count == 0:
                return False, "没有完成的区块分析数据"
        
        # 阶段2不是必须的，但如果存在会被使用
        return True, ""
    
    def execute(self) -> bool:
        """执行大纲生成"""
        try:
            # 1. 收集内部分析数据
            self.update_progress("收集内部分析数据")
            internal_data = self._collect_internal_data()
            
            # 2. 收集外部研究数据
            self.update_progress("收集外部研究数据")
            external_data = self._collect_external_data()
            
            # 3. 生成融合大纲
            self.update_progress("生成融合大纲")
            outline_result = self._generate_outline(internal_data, external_data)
            
            if not outline_result:
                return False
            
            # 4. 保存大纲数据
            self.update_progress("保存大纲数据")
            self._save_outline(outline_result)
            
            return True
            
        except Exception as e:
            self.logger.error(f"执行阶段4时出错: {e}")
            return False
    
    def _collect_internal_data(self) -> Dict[str, Any]:
        """收集内部分析数据"""
        data = {
            'video_info': {},
            'chunk_summaries': [],
            'global_summary': '',
            'key_themes': ''
        }
        
        with self.db_manager.get_connection() as conn:
            # 获取视频基本信息
            cursor = conn.execute(
                "SELECT * FROM video_meta WHERE id = ?",
                (self.video_id,)
            )
            video_row = cursor.fetchone()
            if video_row:
                data['video_info'] = dict(video_row)
                data['global_summary'] = video_row['global_summary'] or ''
                data['key_themes'] = video_row['key_themes'] or ''
            
            # 获取区块摘要
            cursor = conn.execute(
                """SELECT start_time, end_time, chunk_summary FROM chunks 
                   WHERE video_id = ? AND status = 'completed'
                   ORDER BY start_time""",
                (self.video_id,)
            )
            
            for row in cursor.fetchall():
                if row[2]:  # chunk_summary不为空
                    data['chunk_summaries'].append({
                        'start_time': row[0],
                        'end_time': row[1],
                        'summary': row[2]
                    })
        
        return data
    
    def _collect_external_data(self) -> List[Dict[str, Any]]:
        """收集外部研究数据"""
        external_data = []
        
        with self.db_manager.get_connection() as conn:
            cursor = conn.execute(
                """SELECT related_entity, ai_summary FROM research 
                   WHERE video_id = ? AND status = 'completed' AND ai_summary IS NOT NULL""",
                (self.video_id,)
            )
            
            for row in cursor.fetchall():
                if row[1]:  # ai_summary不为空
                    external_data.append({
                        'entity': row[0],
                        'summary': row[1]
                    })
        
        return external_data
    
    def _generate_outline(self, internal_data: Dict[str, Any], 
                         external_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成融合大纲"""
        try:
            from config.prompts import OUTLINE_GENERATION_PROMPT
            
            # 准备内部摘要文本
            internal_summaries = []
            for i, chunk in enumerate(internal_data['chunk_summaries']):
                time_range = f"{chunk['start_time']:.1f}s - {chunk['end_time']:.1f}s"
                internal_summaries.append(f"章节 {i+1} ({time_range}): {chunk['summary']}")
            
            internal_text = "\n".join(internal_summaries)
            
            # 准备外部研究文本
            external_summaries = []
            for research in external_data:
                external_summaries.append(f"关于 {research['entity']}: {research['summary']}")
            
            external_text = "\n".join(external_summaries) if external_summaries else "暂无外部研究资料"
            
            # 生成提示词
            prompt = OUTLINE_GENERATION_PROMPT.format(
                internal_summaries=internal_text,
                external_research=external_text
            )
            
            # 调用AI生成大纲
            result = ai_client.call_ai_json(prompt)
            
            if 'error' in result:
                self.logger.error(f"AI生成大纲失败: {result['error']}")
                return None
            
            # 验证结果格式
            if 'theme' not in result or 'chapters' not in result:
                self.logger.error("AI返回的大纲格式不正确")
                return None
            
            if not isinstance(result['chapters'], list):
                self.logger.error("AI返回的章节不是列表格式")
                return None
            
            self.logger.info(f"成功生成大纲，主题: {result['theme']}, 章节数: {len(result['chapters'])}")
            return result
            
        except Exception as e:
            self.logger.error(f"生成大纲失败: {e}")
            return None
    
    def _save_outline(self, outline_data: Dict[str, Any]):
        """保存大纲数据"""
        with self.db_manager.get_connection() as conn:
            # 保存大纲主记录
            cursor = conn.execute(
                """INSERT INTO outlines (video_id, theme, status)
                   VALUES (?, ?, 'completed')""",
                (self.video_id, outline_data['theme'])
            )
            outline_id = cursor.lastrowid
            
            # 保存章节
            chapters = outline_data['chapters']
            for chapter in chapters:
                conn.execute(
                    """INSERT INTO chapters 
                       (outline_id, chapter_number, title, summary, status)
                       VALUES (?, ?, ?, ?, 'pending')""",
                    (
                        outline_id,
                        chapter.get('chapter_number', 0),
                        chapter.get('title', ''),
                        chapter.get('summary', '')
                    )
                )
            
            conn.commit()
            
            self.logger.info(f"保存大纲完成，outline_id: {outline_id}, 章节数: {len(chapters)}")
    
    def get_latest_outline(self) -> Dict[str, Any]:
        """获取最新的大纲"""
        with self.db_manager.get_connection() as conn:
            # 获取最新的大纲
            cursor = conn.execute(
                """SELECT * FROM outlines 
                   WHERE video_id = ? AND status = 'completed'
                   ORDER BY created_at DESC LIMIT 1""",
                (self.video_id,)
            )
            outline_row = cursor.fetchone()
            
            if not outline_row:
                return None
            
            outline = dict(outline_row)
            
            # 获取章节
            cursor = conn.execute(
                """SELECT * FROM chapters 
                   WHERE outline_id = ?
                   ORDER BY chapter_number""",
                (outline['id'],)
            )
            
            chapters = [dict(row) for row in cursor.fetchall()]
            outline['chapters'] = chapters
            
            return outline
