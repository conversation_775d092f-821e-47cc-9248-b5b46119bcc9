"""
AI驱动的自动化视频剪辑项目主程序
支持从任意阶段开始执行的模块化处理流程
"""

import argparse
import signal
import sys
from typing import Optional

from config.settings import settings
from database.models import DatabaseManager
from stages.stage1_analysis import Stage1Analysis
from stages.stage2_character_identification import Stage2CharacterIdentification
from stages.stage3_consolidation import Stage3Consolidation
from stages.stage4_research import Stage4Research
from stages.stage5_outline import Stage5Outline
from stages.stage6_production import Stage6Production
from utils.logger import get_logger
from utils.shared_state import shutdown_event
from utils.video_utils import video_processor

logger = get_logger(__name__)


def signal_handler(sig, frame):
    """处理中断信号"""
    logger.warning("接收到中断信号，正在准备优雅关闭...")
    shutdown_event.set()


class AutoCutter:
    """自动化视频剪辑主控制器"""

    def __init__(self):
        self.db_manager = DatabaseManager()
        self.stages = {
            1: Stage1Analysis,
            2: Stage2CharacterIdentification,
            3: Stage3Consolidation,
            4: Stage4Research,
            5: Stage5Outline,
            6: Stage6Production,
        }

    def list_videos(self):
        """列出所有视频记录"""
        videos = self.db_manager.get_all_videos()

        if not videos:
            print("没有找到任何视频记录")
            return

        print("\n视频列表:")
        print("-" * 80)
        print(f"{'ID':<5} {'视频名称':<30} {'时长(秒)':<10} {'状态':<15} {'创建时间':<20}")
        print("-" * 80)

        for video in videos:
            print(
                f"{video['id']:<5} {video['video_name']:<30} {video['total_duration']:<10.1f} {video['status']:<15} {video['created_at']:<20}"
            )

    def show_status(self, video_id: int):
        """显示视频处理状态"""
        # 获取视频信息
        video = self.db_manager.get_video_by_id(video_id)
        if not video:
            print(f"未找到ID为 {video_id} 的视频")
            return

        print(f"\n视频信息: {video['video_name']}")
        print("-" * 60)
        print(f"时长: {video['total_duration']:.1f} 秒")
        print(f"全局摘要: {video['global_summary'] or '未生成'}")
        print(f"关键主题: {video['key_themes'] or '未生成'}")

        # 获取各阶段状态
        print("\n处理阶段状态:")
        print("-" * 60)

        for stage_num in range(1, 7):
            status = self.db_manager.get_stage_status(video_id, stage_num)
            if status:
                status_text = status["status"]
                if status["started_at"]:
                    status_text += f" (开始: {status['started_at'][:19]})"
                if status["completed_at"]:
                    status_text += f" (完成: {status['completed_at'][:19]})"
                if status["error_message"]:
                    status_text += f" (错误: {status['error_message']})"

                print(f"阶段 {stage_num}: {status['stage_name']} - {status_text}")
            else:
                print(f"阶段 {stage_num}: 未找到状态记录")

    def create_video_record(
        self, video_name: str, duration: float, file_hash: str, input_file_path: Optional[str] = None
    ) -> int:
        """创建新的视频记录"""
        video_id = self.db_manager.create_video_record(video_name, duration, file_hash, input_file_path)
        logger.info(f"创建视频记录: {video_name} (ID: {video_id})")
        return video_id

    def run_stage(self, video_id: int, stage_number: int, force_level: Optional[str] = None) -> bool:
        """运行指定阶段"""
        if stage_number not in self.stages:
            logger.error(f"无效的阶段编号: {stage_number}")
            return False

        # 如果强制执行，先重置状态，无论级别是'soft'还是'full'
        if force_level:
            logger.warning(f"--- 强制执行模式 (级别: {force_level}) ---")
            self.db_manager.reset_stage_status(video_id, stage_number)

        # 检查视频是否存在
        if not self.db_manager.get_video_by_id(video_id):
            logger.error(f"未找到ID为 {video_id} 的视频")
            return False

        # 创建阶段实例
        stage_class = self.stages[stage_number]
        stage = stage_class(self.db_manager, video_id)

        # 将 force_level 传递给 stage 实例，以便在 execute 方法中使用
        if force_level:
            stage.force_level = force_level

        logger.info(f"开始运行阶段 {stage_number}: {stage.stage_name}")
        success = stage.run()

        if success:
            logger.info(f"阶段 {stage_number} 运行成功")
        else:
            logger.error(f"阶段 {stage_number} 运行失败")

        return success

    def run_all_stages(self, video_id: int, start_from: int = 1) -> bool:
        """运行所有阶段"""
        logger.info(f"开始运行所有阶段，从阶段 {start_from} 开始")

        for stage_number in range(start_from, 7):  # 阶段总数变为6，所以范围到7
            # 注意：强制执行不适用于'all'模式
            if not self.run_stage(video_id, stage_number, force_level=None):
                logger.error(f"阶段 {stage_number} 失败，停止后续处理")
                return False

        logger.info("所有阶段运行完成")
        return True

    def import_videos(self):
        """从输入目录扫描并导入新视频"""
        logger.info(f"开始从目录 '{settings.INPUT_DIR}' 导入视频...")
        supported_formats = [".mp4", ".mov", ".avi", ".mkv"]
        video_files = []
        for fmt in supported_formats:
            video_files.extend(settings.INPUT_DIR.glob(f"*{fmt}"))

        if not video_files:
            logger.warning("在输入目录中没有找到支持的视频文件。")
            return

        video_files.sort()
        imported_count = 0
        for video_file in video_files:
            video_path_str = str(video_file.resolve())

            logger.info(f"发现新视频: {video_file.name}, 开始计算哈希值...")
            try:
                file_hash = video_processor.calculate_file_hash(video_file)
            except Exception as e:
                logger.error(f"无法计算 '{video_file.name}' 的哈希值，跳过。错误: {e}")
                continue

            # 通过哈希检查视频内容是否已存在
            if self.db_manager.get_video_by_hash(file_hash):
                logger.warning(
                    f"内容与视频 '{video_file.name}' 相同的视频已存在于数据库中 (哈希: {file_hash[:12]}...), 跳过。"
                )
                continue

            logger.info(f"开始提取视频信息: {video_file.name}...")
            video_info = video_processor.get_video_info(video_file)
            if not video_info:
                logger.error(f"无法获取 '{video_file.name}' 的视频信息，跳过。")
                continue

            video_id = self.db_manager.create_video_record(
                video_name=video_file.stem,
                total_duration=video_info["duration"],
                file_hash=file_hash,
                input_file_path=video_path_str,
            )
            logger.info(f"✅ 成功导入视频 '{video_file.stem}'，ID: {video_id}，时长: {video_info['duration']:.1f}秒。")
            imported_count += 1

        logger.info(f"导入完成。新增 {imported_count} 个视频。")


def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    parser = argparse.ArgumentParser(
        description="AI驱动的自动化视频剪辑工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py --import-videos                  # 从输入目录导入新视频
  python main.py --list                           # 列出所有视频
  python main.py --status 1                       # 查看视频1的状态
  python main.py --video 1 --stage 1              # 运行视频1的阶段1
  python main.py --video 1 --stage all            # 运行视频1的所有阶段
  python main.py --video 1 --stage all --from 2   # 从阶段2开始运行所有阶段
        """,
    )

    # 基本操作
    parser.add_argument("--list", action="store_true", help="列出所有视频记录")
    parser.add_argument("--status", type=int, metavar="VIDEO_ID", help="显示指定视频的处理状态")
    parser.add_argument("--import-videos", action="store_true", help="从输入目录导入新视频")

    # 处理操作
    parser.add_argument("--video", type=int, metavar="VIDEO_ID", help="指定要处理的视频ID")
    parser.add_argument("--stage", metavar="STAGE", help="要运行的阶段 (1-6 或 'all')")
    parser.add_argument(
        "--from",
        type=int,
        metavar="START_STAGE",
        dest="start_from",
        default=1,
        help="当使用 --stage all时，从指定阶段开始 (默认: 1)",
    )
    parser.add_argument(
        "--force",
        "-f",
        nargs="?",
        const="soft",
        choices=["soft", "full"],
        help="强制运行一个阶段。'soft' (默认): 重新分析，但使用缓存的场景检测结果。'full': 完全重新运行，包括场景检测。",
    )

    args = parser.parse_args()

    # 验证配置
    config_errors = settings.validate()
    if config_errors:
        logger.error("配置验证失败:")
        for error in config_errors:
            logger.error(f"  - {error}")
        sys.exit(1)

    # 创建主控制器
    auto_cutter = AutoCutter()

    try:
        # 处理命令
        if args.list:
            auto_cutter.list_videos()

        elif args.status:
            auto_cutter.show_status(args.status)

        elif args.import_videos:
            auto_cutter.import_videos()

        elif args.video and args.stage:
            video_id = args.video

            if args.stage == "all":
                # 注意：为避免意外，`--force` 对 `all` 无效
                if args.force:
                    logger.warning("`--force` 标志不能与 `--stage all` 一起使用。请对单个阶段使用强制执行。")
                success = auto_cutter.run_all_stages(video_id, args.start_from)
            else:
                try:
                    stage_number = int(args.stage)
                    if stage_number not in range(1, 7):
                        raise ValueError()
                    success = auto_cutter.run_stage(video_id, stage_number, force_level=args.force)
                except ValueError:
                    logger.error("阶段编号必须是 1-6 或 'all'")
                    sys.exit(1)

            if success:
                print("处理完成")
            else:
                print("处理失败")
                sys.exit(1)

        else:
            parser.print_help()

    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
