# Auto-Cutter 使用指南

## 项目概述

Auto-Cutter 是一个 AI 驱动的自动化视频剪辑工具，支持从任意阶段开始执行的模块化处理流程。

## 四个处理阶段

1. **阶段 1：三层深度分析与数据库构建**

   - 场景检测和微观分析
   - 宏观区块分析
   - 全局分析

2. **阶段 2：外部资料增强**

   - 提取关键实体
   - 网络搜索相关信息
   - AI 总结外部资料

3. **阶段 3：融合知识生成全局大纲**

   - 融合内部分析和外部资料
   - 生成叙事大纲
   - 创建章节结构

4. **阶段 4：自动化生产与合成**
   - 片段检索
   - 文案创作和配音
   - 最终视频合成

## 安装和配置

### 1. 安装依赖

```bash
# 使用 uv 安装依赖
uv sync

# 或使用 pip
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制配置文件模板
cp .env.example .env

# 编辑配置文件
vim .env
```

必需配置：

- `VOLCENGINE_API_KEY`: OpenAI API 密钥

可选配置：

- `VOLCENGINE_BASE_URL`: API 基础 URL（默认为 OpenAI 官方）
- `VOLCENGINE_MODEL_NAME`: 使用的模型（默认为 gpt-4）
- `CHUNK_DURATION_MINUTES`: 区块时长（默认 15 分钟）

## 使用方法

### 基本命令

```bash
# 查看帮助
python main.py --help

# 列出所有视频记录
python main.py --list

# 查看指定视频的状态
python main.py --status 1

# 创建新视频记录
python main.py --create "我的视频" 3600
```

### 处理命令

```bash
# 运行指定视频的指定阶段
python main.py --video 1 --stage 1

# 运行指定视频的所有阶段
python main.py --video 1 --stage all

# 从指定阶段开始运行所有阶段
python main.py --video 1 --stage all --from 2
```

## 使用流程示例

### 完整流程

```bash
# 1. 创建视频记录
python main.py --create "电影分析" 7200

# 2. 运行所有阶段
python main.py --video 1 --stage all

# 3. 查看处理状态
python main.py --status 1
```

### 分步执行

```bash
# 1. 创建视频记录
python main.py --create "电影分析" 7200

# 2. 只运行阶段1（三层分析）
python main.py --video 1 --stage 1

# 3. 查看状态
python main.py --status 1

# 4. 运行阶段2（外部资料增强）
python main.py --video 1 --stage 2

# 5. 运行阶段3（生成大纲）
python main.py --video 1 --stage 3

# 6. 运行阶段4（视频合成）
python main.py --video 1 --stage 4
```

### 从中间阶段开始

```bash
# 如果阶段1已完成，从阶段2开始
python main.py --video 1 --stage all --from 2

# 如果前3个阶段已完成，只运行阶段4
python main.py --video 1 --stage 4
```

## 目录结构

```
auto-cutter/
├── main.py                 # 主程序入口
├── config/                 # 配置模块
│   ├── settings.py         # 配置管理
│   └── prompts.py          # AI提示词模板
├── database/               # 数据库模块
│   └── models.py           # 数据库模型
├── stages/                 # 处理阶段
│   ├── base.py             # 基础阶段类
│   ├── stage1_analysis.py  # 阶段1：分析
│   ├── stage2_research.py  # 阶段2：研究
│   ├── stage3_outline.py   # 阶段3：大纲
│   └── stage4_production.py # 阶段4：制作
├── utils/                  # 工具模块
│   ├── logger.py           # 日志工具
│   └── ai_utils.py         # AI工具
├── data/                   # 数据目录
│   └── analysis.db         # SQLite数据库
├── clips/                  # 视频片段目录
├── output/                 # 输出目录
└── logs/                   # 日志目录
```

## 数据库结构

项目使用 SQLite 数据库存储所有分析数据：

- `video_meta`: 视频元信息
- `scenes`: 场景分析数据
- `chunks`: 区块分析数据
- `research`: 外部研究数据
- `outlines`: 大纲数据
- `chapters`: 章节数据
- `processing_status`: 处理状态跟踪

## 注意事项

1. **API 配置**: 确保正确配置 OpenAI API 密钥
2. **视频片段**: 如果 clips 目录中没有片段，程序会尝试进行场景检测
3. **错误处理**: 程序具有重试机制和错误恢复功能
4. **断点续传**: 支持从任意阶段开始执行
5. **日志记录**: 所有操作都会记录详细日志

## 扩展功能

### 添加新的 AI 模型

在 `config/settings.py` 中修改模型配置：

```python
VOLCENGINE_MODEL_NAME = "gpt-4-turbo"
VOLCENGINE_BASE_URL = "https://api.openai.com/v1"
```

### 自定义提示词

在 `config/prompts.py` 中修改 AI 提示词模板。

### 添加新的处理阶段

1. 在 `stages/` 目录下创建新的阶段类
2. 继承 `BaseStage` 类
3. 实现必需的方法
4. 在 `main.py` 中注册新阶段

## 故障排除

### 常见问题

1. **API 密钥错误**: 检查 `.env` 文件中的 `VOLCENGINE_API_KEY`
2. **数据库错误**: 删除 `data/analysis.db` 重新初始化
3. **片段缺失**: 确保 `clips/` 目录中有视频片段文件
4. **权限错误**: 确保程序有读写相关目录的权限

### 查看日志

```bash
# 查看最新日志
tail -f logs/auto_cutter_$(date +%Y%m%d).log
```

## 开发计划

- [ ] 实现真实的场景检测功能
- [ ] 集成实际的搜索引擎 API
- [ ] 添加 TTS 语音合成功能
- [ ] 实现真实的视频合成功能
- [ ] 添加 Web 界面
- [ ] 支持更多视频格式
- [ ] 添加批量处理功能
