[project]
name = "auto-cutter"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12,<3.13"
dependencies = [
    "moviepy>=2.2.1",
    "numpy<2",
    "openai>=1.90.0",
    "opencv-python>=*********",
    "python-dotenv>=1.1.0",
    "requests>=2.32.4",
    "scenedetect[opencv]>=0.6.6",
    "tavily-python>=0.7.8",
    "tos>=2.8.4",
    "psycopg2-binary>=2.9.9",
    "sqlalchemy>=2.0",            # <-- 新增
    "alembic>=1.13",              # <-- 新增
    "beautifulsoup4>=4.13.4",
    "tensorflow-macos>=2.16.2",
    "tensorflow-metal>=1.2.0",
    "deepface>=0.0.93",
    "scikit-learn>=1.7.0",
    "tf-keras>=2.16.0",
    "tensorflow>=2.16.2",
]

[tool.ruff]
line-length = 120

[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
